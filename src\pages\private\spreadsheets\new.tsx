import Head from 'next/head';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/context/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

export default function NewSpreadsheetPage() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [embedUrl, setEmbedUrl] = useState('');
  const [category, setCategory] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { user } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Add the spreadsheet to Firestore
      await addDoc(collection(db, 'spreadsheets'), {
        title,
        description,
        embedUrl,
        category,
        createdBy: user?.displayName || user?.email || 'Anonymous',
        createdById: user?.uid,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      // Log activity
      await addDoc(collection(db, 'activity'), {
        type: 'spreadsheet',
        title: `Added spreadsheet: ${title}`,
        user: user?.displayName || user?.email || 'Anonymous',
        timestamp: Timestamp.now()
      });

      router.push('/private/spreadsheets');
    } catch (err: any) {
      console.error('Error adding spreadsheet:', err);
      setError(err.message || 'Failed to add spreadsheet');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <Head>
        <title>Add New Spreadsheet | Parallax Interactive</title>
        <meta name="description" content="Add a new embedded spreadsheet to the Parallax Interactive developer portal" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-4xl font-bold mb-8">Add New Spreadsheet</h1>

            {error && (
              <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg p-6">
              <div className="mb-6">
                <label htmlFor="title" className="block mb-2 font-semibold">Title</label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="description" className="block mb-2 font-semibold">Description</label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  required
                ></textarea>
              </div>

              <div className="mb-6">
                <label htmlFor="embedUrl" className="block mb-2 font-semibold">Embed URL</label>
                <input
                  type="url"
                  id="embedUrl"
                  value={embedUrl}
                  onChange={(e) => setEmbedUrl(e.target.value)}
                  placeholder="https://docs.google.com/spreadsheets/d/e/..."
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  required
                />
                <p className="mt-2 text-sm text-gray-400">
                  Paste the embed URL from Google Sheets, Microsoft Excel Online, Airtable, or any other spreadsheet tool.
                </p>
              </div>

              <div className="mb-6">
                <label htmlFor="category" className="block mb-2 font-semibold">Category</label>
                <select
                  id="category"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  required
                >
                  <option value="" disabled>Select a category</option>
                  <option value="Budget">Budget</option>
                  <option value="Timeline">Timeline</option>
                  <option value="Assets">Assets</option>
                  <option value="Project Management">Project Management</option>
                  <option value="Data Analysis">Data Analysis</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => router.push('/private/spreadsheets')}
                  className="px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg transition-all"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-3 bg-emerald-600 hover:bg-emerald-700 rounded-lg font-bold transition-all disabled:opacity-50"
                >
                  {loading ? 'Adding...' : 'Add Spreadsheet'}
                </button>
              </div>
            </form>
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
