import Head from 'next/head';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface Spreadsheet {
  id: string;
  title: string;
  description: string;
  embedUrl: string;
  category: string;
  createdBy: string;
  createdAt: string;
}

export default function SpreadsheetsPage() {
  const [spreadsheets, setSpreadsheets] = useState<Spreadsheet[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    const fetchSpreadsheets = async () => {
      try {
        const q = query(
          collection(db, 'spreadsheets'),
          orderBy('createdAt', 'desc')
        );

        const snapshot = await getDocs(q);
        const sheetsData = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            description: data.description,
            embedUrl: data.embedUrl,
            category: data.category,
            createdBy: data.createdBy,
            createdAt: data.createdAt.toDate().toLocaleDateString()
          };
        });

        setSpreadsheets(sheetsData);

        // Extract unique categories
        const uniqueCategories = Array.from(new Set(sheetsData.map(sheet => sheet.category)));
        setCategories(uniqueCategories);
      } catch (error) {
        console.error('Error fetching spreadsheets:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSpreadsheets();
  }, []);

  const filteredSpreadsheets = activeCategory
    ? spreadsheets.filter(sheet => sheet.category === activeCategory)
    : spreadsheets;

  return (
    <ProtectedRoute>
      <Head>
        <title>Spreadsheets | Parallax Interactive</title>
        <meta name="description" content="Embedded spreadsheets for project management and tracking" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-4xl font-bold">Embedded Spreadsheets</h1>
              <Link href="/private/spreadsheets/new" className="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded-lg transition-all">
                Add New Spreadsheet
              </Link>
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-3 mb-8">
              <button
                onClick={() => setActiveCategory(null)}
                className={`px-4 py-2 rounded-full text-sm ${
                  activeCategory === null
                    ? 'bg-emerald-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                All Categories
              </button>

              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm ${
                    activeCategory === category
                      ? 'bg-emerald-600 text-white'
                      : 'bg-gray-700 hover:bg-gray-600'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-emerald-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredSpreadsheets.map(sheet => (
                  <Link
                    key={sheet.id}
                    href={`/private/spreadsheets/${sheet.id}`}
                    className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all"
                  >
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-xl font-bold">{sheet.title}</h3>
                        <span className="bg-emerald-900 text-emerald-300 text-xs px-2 py-1 rounded-full">
                          {sheet.category}
                        </span>
                      </div>
                      <p className="text-gray-300 mb-4">{sheet.description}</p>
                      <div className="mt-4 text-sm text-gray-400 flex justify-between">
                        <span>{sheet.createdBy}</span>
                        <span>{sheet.createdAt}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
