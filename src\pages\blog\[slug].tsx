import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { collection, query, where, getDocs, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Markdown from 'react-markdown';

interface BlogPost {
  id: string;
  title: string;
  content: string;
  author: string;
  publishDate: string;
  imageUrl: string;
  isPublic: boolean;
}

export default function BlogPostPage() {
  const router = useRouter();
  const { slug } = router.query;
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchBlogPost = async () => {
      if (!slug) return;

      try {
        const q = query(
          collection(db, 'blog'),
          where('slug', '==', slug),
          where('isPublic', '==', true),
          limit(1)
        );

        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          setError('Blog post not found');
          setLoading(false);
          return;
        }

        const doc = snapshot.docs[0];
        const data = doc.data();

        setPost({
          id: doc.id,
          title: data.title,
          content: data.content,
          author: data.author,
          publishDate: data.publishDate.toDate().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          imageUrl: data.imageUrl || '/images/blog-placeholder.jpg',
          isPublic: data.isPublic
        });
      } catch (error) {
        console.error('Error fetching blog post:', error);
        setError('Failed to load blog post');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPost();
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />
        <div className="container mx-auto max-w-4xl py-20 px-6 text-center">
          <h1 className="text-4xl font-bold mb-6">{error}</h1>
          <p className="mb-8">The blog post you're looking for could not be found.</p>
          <Link href="/blog" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all">
            Back to Blog
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  if (!post) return null;

  return (
    <>
      <Head>
        <title>{post.title} | Parallax Interactive Blog</title>
        <meta name="description" content={post.content.substring(0, 160)} />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        <article className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <Link href="/blog" className="inline-flex items-center text-blue-400 hover:text-blue-300 mb-8">
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Blog
            </Link>

            <h1 className="text-4xl md:text-5xl font-bold mb-6">{post.title}</h1>

            <div className="flex items-center mb-8 text-gray-400">
              <span className="mr-4">{post.author}</span>
              <span>{post.publishDate}</span>
            </div>

            <div className="relative h-96 w-full mb-10">
              <Image
                src={post.imageUrl}
                alt={post.title}
                fill
                className="object-cover rounded-lg"
              />
            </div>

            <div className="prose prose-lg prose-invert max-w-none">
              <Markdown>{post.content}</Markdown>
            </div>
          </div>
        </article>

        <Footer />
      </div>
    </>
  );
}
