import Head from 'next/head';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface MilestoneItem {
  id: string;
  title: string;
  description: string;
  targetDate: string;
  completed: boolean;
  projectId: string;
  projectName: string;
}

export default function RoadmapPage() {
  const [milestones, setMilestones] = useState<MilestoneItem[]>([]);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [projects, setProjects] = useState<{id: string, name: string}[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRoadmapData = async () => {
      try {
        // Fetch projects first
        const projectsSnapshot = await getDocs(collection(db, 'projects'));
        const projectsData = projectsSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().title
        }));
        setProjects(projectsData);

        // Fetch milestones
        const milestonesQuery = query(
          collection(db, 'milestones'),
          orderBy('targetDate', 'asc')
        );

        const milestonesSnapshot = await getDocs(milestonesQuery);
        const milestonesData = milestonesSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            description: data.description,
            targetDate: data.targetDate.toDate().toISOString().split('T')[0],
            completed: data.completed,
            projectId: data.projectId,
            projectName: projectsData.find(p => p.id === data.projectId)?.name || 'Unknown Project'
          };
        });

        setMilestones(milestonesData);
      } catch (error) {
        console.error('Error fetching roadmap data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRoadmapData();
  }, []);

  const filteredMilestones = activeFilter
    ? milestones.filter(ms => ms.projectId === activeFilter)
    : milestones;

  return (
    <>
      <Head>
        <title>Development Roadmap | Parallax Interactive</title>
        <meta name="description" content="Track our game development progress and upcoming milestones" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        <section className="py-20 px-6">
          <div className="container mx-auto max-w-6xl">
            <h1 className="text-5xl font-bold mb-8 text-center">Development Roadmap</h1>
            <p className="text-xl mb-12 text-center max-w-3xl mx-auto">
              Follow our journey as we build and improve our games. This roadmap outlines
              our planned features, updates, and release milestones.
            </p>

            {/* Project Filters */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <button
                onClick={() => setActiveFilter(null)}
                className={`px-4 py-2 rounded-full ${
                  activeFilter === null
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 hover:bg-gray-700'
                }`}
              >
                All Projects
              </button>

              {projects.map(project => (
                <button
                  key={project.id}
                  onClick={() => setActiveFilter(project.id)}
                  className={`px-4 py-2 rounded-full ${
                    activeFilter === project.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-800 hover:bg-gray-700'
                  }`}
                >
                  {project.name}
                </button>
              ))}
            </div>

            {loading ? (
              <div className="flex justify-center my-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-blue-600"></div>

                <div className="space-y-12">
                  {filteredMilestones.map((milestone, index) => (
                    <div
                      key={milestone.id}
                      className={`relative flex ${
                        index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                      }`}
                    >
                      <div className="w-1/2"></div>

                      {/* Circle on timeline */}
                      <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-4 border-blue-600 bg-gray-900 z-10"></div>

                      <div className={`w-1/2 ${
                        index % 2 === 0 ? 'pl-10' : 'pr-10'
                      }`}>
                        <div className={`bg-gray-800 p-6 rounded-lg border-l-4 ${
                          milestone.completed ? 'border-green-500' : 'border-blue-600'
                        }`}>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-semibold py-1 px-3 rounded-full bg-gray-700">
                              {milestone.projectName}
                            </span>
                            <span className="text-sm font-mono">
                              {milestone.targetDate}
                            </span>
                          </div>
                          <h3 className="text-xl font-bold mb-2 flex items-center">
                            {milestone.title}
                            {milestone.completed && (
                              <svg className="h-5 w-5 text-green-500 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </h3>
                          <p className="text-gray-300">{milestone.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
}
