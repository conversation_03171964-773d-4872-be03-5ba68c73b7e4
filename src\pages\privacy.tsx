import Head from 'next/head';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

export default function PrivacyPolicyPage() {
  return (
    <>
      <Head>
        <title>Privacy Policy | Parallax Interactive</title>
        <meta name="description" content="Parallax Interactive's Privacy Policy" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        <section className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-5xl font-bold mb-8 text-center">Privacy Policy</h1>

            <div className="prose prose-lg prose-invert mx-auto">
              <p>
                Your privacy is important to us. This privacy policy explains how Parallax Interactive collects, uses, and protects your personal information.
              </p>

              <h2>Information We Collect</h2>
              <p>
                We collect information in the following ways:
              </p>
              <ul>
                <li>
                  <strong>Information you provide directly:</strong> When you contact us, donate, or create an account, we collect information such as your name, email address, and message.
                </li>
                <li>
                  <strong>Automatically collected information:</strong> We may collect information about your device and usage, such as IP address, browser type, and pages visited.
                </li>
              </ul>

              <h2>How We Use Your Information</h2>
              <p>
                We use your information to:
              </p>
              <ul>
                <li>
                  Respond to your inquiries and provide support.
                </li>
                <li>
                  Process donations and provide updates on our projects.
                </li>
                <li>
                  Improve our website and services.
                </li>
              </ul>

              <h2>Data Security</h2>
              <p>
                We take reasonable measures to protect your personal information from unauthorized access, use, or disclosure.
              </p>

              <h2>Third-Party Services</h2>
              <p>
                We may use third-party services such as Firebase, which have their own privacy policies.
              </p>

              <h2>Changes to This Policy</h2>
              <p>
                We may update this privacy policy from time to time. We will notify you of any significant changes.
              </p>

              <h2>Contact Us</h2>
              <p>
                If you have any questions about this privacy policy, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
              </p>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
}
