import Head from "next/head";
import { useEffect, useState } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import ProtectedRoute from "@/components/ProtectedRoute";
import PrivateNavbar from "@/components/PrivateNavbar";
import Footer from "@/components/Footer";
import { useAuth } from "@/context/AuthContext";

interface User {
  id: string;
  email: string;
  role: "admin" | "developer" | "viewer";
  authorized: boolean;
  lastLogin: string | null;
  dateAdded: string;
}

export default function UserManagementPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserRole, setNewUserRole] = useState<
    "admin" | "developer" | "viewer"
  >("developer");
  const [modalOpen, setModalOpen] = useState(false);
  const [processingUser, setProcessingUser] = useState("");
  const { user: currentUser } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!currentUser) return;

      try {
        const userDoc = await getDocs(
          query(
            collection(db, "users"),
            where("email", "==", currentUser.email),
          ),
        );

        if (!userDoc.empty) {
          const userData = userDoc.docs[0].data();
          setIsAdmin(userData.role === "admin");
        } else {
          setIsAdmin(false);
        }
      } catch (err) {
        console.error("Error checking admin status:", err);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [currentUser]);

  useEffect(() => {
    if (!isAdmin) return;

    const fetchUsers = async () => {
      try {
        const snapshot = await getDocs(collection(db, "users"));
        const usersData = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            email: data.email,
            role: data.role,
            authorized: data.authorized,
            lastLogin: data.lastLogin
              ? new Date(data.lastLogin.toDate()).toLocaleString()
              : null,
            dateAdded: new Date(data.dateAdded.toDate()).toLocaleString(),
          };
        });

        setUsers(usersData);
      } catch (err) {
        console.error("Error fetching users:", err);
        setError("Failed to load users");
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [isAdmin]);

  const toggleUserAuthorization = async (
    userId: string,
    currentStatus: boolean,
  ) => {
    if (!isAdmin) return;

    setProcessingUser(userId);
    try {
      await updateDoc(doc(db, "users", userId), {
        authorized: !currentStatus,
      });

      setUsers(
        users.map((user) =>
          user.id === userId ? { ...user, authorized: !currentStatus } : user,
        ),
      );
    } catch (err) {
      console.error("Error updating user authorization:", err);
      setError("Failed to update user authorization");
    } finally {
      setProcessingUser("");
    }
  };

  const updateUserRole = async (
    userId: string,
    newRole: "admin" | "developer" | "viewer",
  ) => {
    if (!isAdmin) return;

    setProcessingUser(userId);
    try {
      await updateDoc(doc(db, "users", userId), {
        role: newRole,
      });

      setUsers(
        users.map((user) =>
          user.id === userId ? { ...user, role: newRole } : user,
        ),
      );
    } catch (err) {
      console.error("Error updating user role:", err);
      setError("Failed to update user role");
    } finally {
      setProcessingUser("");
    }
  };

  const deleteUser = async (userId: string) => {
    if (!isAdmin) return;
    if (!confirm("Are you sure you want to delete this user?")) return;

    setProcessingUser(userId);
    try {
      await deleteDoc(doc(db, "users", userId));
      setUsers(users.filter((user) => user.id !== userId));
    } catch (err) {
      console.error("Error deleting user:", err);
      setError("Failed to delete user");
    } finally {
      setProcessingUser("");
    }
  };

  const addNewUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAdmin) return;

    try {
      await addDoc(collection(db, "users"), {
        email: newUserEmail,
        role: newUserRole,
        authorized: true,
        dateAdded: new Date(),
        lastLogin: null,
      });

      setNewUserEmail("");
      setModalOpen(false);

      // Refresh user list
      const snapshot = await getDocs(collection(db, "users"));
      const usersData = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          email: data.email,
          role: data.role,
          authorized: data.authorized,
          lastLogin: data.lastLogin
            ? new Date(data.lastLogin.toDate()).toLocaleString()
            : null,
          dateAdded: new Date(data.dateAdded.toDate()).toLocaleString(),
        };
      });

      setUsers(usersData);
    } catch (err) {
      console.error("Error adding new user:", err);
      setError("Failed to add new user");
    }
  };

  if (!isAdmin) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
          <PrivateNavbar />
          <div className="container mx-auto max-w-4xl px-6 py-20 text-center">
            <h1 className="mb-6 text-4xl font-bold">Access Denied</h1>
            <p className="mb-8">
              You do not have permission to access this page. Only
              administrators can manage users.
            </p>
          </div>
          <Footer />
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Head>
        <title>User Management | Parallax Interactive</title>
        <meta
          name="description"
          content="Manage users and access permissions for Parallax Interactive"
        />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="px-6 py-12">
          <div className="container mx-auto max-w-6xl">
            <div className="mb-8 flex items-center justify-between">
              <h1 className="text-4xl font-bold">User Management</h1>
              <button
                onClick={() => setModalOpen(true)}
                className="rounded-lg bg-blue-600 px-4 py-2 font-bold text-white transition-all hover:bg-blue-700"
              >
                Add New User
              </button>
            </div>

            {error && (
              <div className="mb-6 rounded-lg border border-red-500 bg-red-500 bg-opacity-20 p-4 text-red-300">
                {error}
              </div>
            )}

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="overflow-hidden rounded-lg bg-gray-800">
                <table className="w-full text-left">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-6 py-4">Email</th>
                      <th className="px-6 py-4">Role</th>
                      <th className="px-6 py-4">Status</th>
                      <th className="px-6 py-4">Last Login</th>
                      <th className="px-6 py-4">Date Added</th>
                      <th className="px-6 py-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {users.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-750">
                        <td className="px-6 py-4">{user.email}</td>
                        <td className="px-6 py-4">
                          <select
                            value={user.role}
                            onChange={(e) =>
                              updateUserRole(
                                user.id,
                                e.target.value as
                                  | "admin"
                                  | "developer"
                                  | "viewer",
                              )
                            }
                            disabled={processingUser === user.id}
                            className="rounded bg-gray-700 px-2 py-1"
                          >
                            <option value="admin">Admin</option>
                            <option value="developer">Developer</option>
                            <option value="viewer">Viewer</option>
                          </select>
                        </td>
                        <td className="px-6 py-4">
                          <span
                            className={`rounded-full px-3 py-1 text-xs ${
                              user.authorized
                                ? "bg-green-900 text-green-300"
                                : "bg-red-900 text-red-300"
                            }`}
                          >
                            {user.authorized ? "Authorized" : "Revoked"}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          {user.lastLogin || "Never"}
                        </td>
                        <td className="px-6 py-4">{user.dateAdded}</td>
                        <td className="px-6 py-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() =>
                                toggleUserAuthorization(
                                  user.id,
                                  user.authorized,
                                )
                              }
                              disabled={
                                processingUser === user.id ||
                                user.email === currentUser?.email
                              }
                              className={`rounded px-3 py-1 text-xs ${
                                user.authorized
                                  ? "bg-red-600 hover:bg-red-700"
                                  : "bg-green-600 hover:bg-green-700"
                              } disabled:opacity-50`}
                            >
                              {user.authorized ? "Revoke" : "Authorize"}
                            </button>
                            <button
                              onClick={() => deleteUser(user.id)}
                              disabled={
                                processingUser === user.id ||
                                user.email === currentUser?.email
                              }
                              className="rounded bg-gray-600 px-3 py-1 text-xs hover:bg-gray-700 disabled:opacity-50"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </section>

        {/* Add User Modal */}
        {modalOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 px-4">
            <div className="w-full max-w-md rounded-lg bg-gray-800 p-6">
              <h2 className="mb-6 text-2xl font-bold">Add New User</h2>

              <form onSubmit={addNewUser}>
                <div className="mb-6">
                  <label htmlFor="email" className="mb-2 block font-semibold">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={newUserEmail}
                    onChange={(e) => setNewUserEmail(e.target.value)}
                    className="w-full rounded-lg bg-gray-700 px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="role" className="mb-2 block font-semibold">
                    Role
                  </label>
                  <select
                    id="role"
                    value={newUserRole}
                    onChange={(e) =>
                      setNewUserRole(
                        e.target.value as "admin" | "developer" | "viewer",
                      )
                    }
                    className="w-full rounded-lg bg-gray-700 px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="admin">Admin</option>
                    <option value="developer">Developer</option>
                    <option value="viewer">Viewer</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={() => setModalOpen(false)}
                    className="rounded-lg bg-gray-600 px-6 py-3 transition-all hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="rounded-lg bg-blue-600 px-6 py-3 font-bold transition-all hover:bg-blue-700"
                  >
                    Add User
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
