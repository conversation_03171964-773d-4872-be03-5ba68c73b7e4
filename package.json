{"name": "parallax-interactive", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache"}, "dependencies": {"@t3-oss/env-nextjs": "^0.10.1", "@tailwindcss/typography": "^0.5.16", "@toast-ui/editor": "^3.2.2", "autoprefixer": "^10.4.20", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "firebase": "^11.4.0", "framer-motion": "^12.4.7", "geist": "^1.3.0", "next": "^15.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.0.0", "zod": "^3.23.3"}, "devDependencies": {"@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "postcss": "^8.5.3", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.17", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "npm@11.1.0"}