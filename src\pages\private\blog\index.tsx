import Head from 'next/head';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface BlogPost {
  id: string;
  title: string;
  author: string;
  publishDate: string;
  isPublic: boolean;
}

export default function PrivateBlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAllBlogPosts = async () => {
      try {
        const q = query(
          collection(db, 'blog'),
          orderBy('publishDate', 'desc')
        );

        const snapshot = await getDocs(q);
        const blogPosts = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            author: data.author,
            publishDate: data.publishDate.toDate().toLocaleDateString(),
            isPublic: data.isPublic
          };
        });

        setPosts(blogPosts);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAllBlogPosts();
  }, []);

  return (
    <ProtectedRoute>
      <Head>
        <title>Internal Blog | Parallax Interactive</title>
        <meta name="description" content="Internal blog management for Parallax Interactive team" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-4xl font-bold">Internal Blog</h1>
              <Link href="/private/blog/new" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-all">
                Create New Post
              </Link>
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="bg-gray-800 rounded-lg overflow-hidden">
                <table className="w-full text-left">
                  <thead className="bg-gray-700">
                  <tr>
                    <th className="py-4 px-6">Title</th>
                    <th className="py-4 px-6">Author</th>
                    <th className="py-4 px-6">Date</th>
                    <th className="py-4 px-6">Status</th>
                    <th className="py-4 px-6">Actions</th>
                  </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                  {posts.map((post) => (
                    <tr key={post.id} className="hover:bg-gray-750">
                      <td className="py-4 px-6">{post.title}</td>
                      <td className="py-4 px-6">{post.author}</td>
                      <td className="py-4 px-6">{post.publishDate}</td>
                      <td className="py-4 px-6">
                          <span className={`px-3 py-1 rounded-full text-xs ${
                            post.isPublic
                              ? 'bg-green-900 text-green-300'
                              : 'bg-gray-600 text-gray-300'
                          }`}>
                            {post.isPublic ? 'Public' : 'Private'}
                          </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2">
                          <Link href={`/private/blog/edit/${post.id}`} className="text-blue-400 hover:text-blue-300">
                            Edit
                          </Link>
                          <Link href={`/private/blog/view/${post.id}`} className="text-indigo-400 hover:text-indigo-300">
                            View
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
