import Link from 'next/link';
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const { user, isAuthorized } = useAuth();

  return (
    <nav className="bg-gray-900 bg-opacity-90 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-6 py-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold">
            PARALLAX<span className="text-blue-500">INTERACTIVE</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
              About
            </Link>
            <Link href="/donate" className="text-gray-300 hover:text-white transition-colors">
              Support Us
            </Link>
            <Link href="/roadmap" className="text-gray-300 hover:text-white transition-colors">
              Road Map
            </Link>
            <Link href="/blog" className="text-gray-300 hover:text-white transition-colors">
              Dev Blog
            </Link>

            {isAuthorized ? (
              <Link
                href="/private/dashboard"
                className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors"
              >
                Developer Portal
              </Link>
            ) : user ? (
              <Link href="/login" className="text-gray-300 hover:text-white transition-colors">
                Login
              </Link>
            ) : (
              <Link
                href="/login"
                className="border border-gray-600 hover:border-gray-500 px-4 py-2 rounded-lg text-gray-300 hover:text-white transition-colors"
              >
                Login
              </Link>
            )}
          </div>

          {/* Mobile Navigation Button */}
          <button
            className="md:hidden text-gray-300 focus:outline-none"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            {menuOpen ? (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>

        {/* Mobile Navigation Menu */}
        {menuOpen && (
          <div className="md:hidden mt-4 py-4 space-y-4">
            <Link
              href="/about"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              About
            </Link>
            <Link
              href="/donate"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Support Us
            </Link>
            <Link
              href="/roadmap"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Road Map
            </Link>
            <Link
              href="/blog"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Dev Blog
            </Link>

            {isAuthorized ? (
              <Link
                href="/private/dashboard"
                className="block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors"
                onClick={() => setMenuOpen(false)}
              >
                Developer Portal
              </Link>
            ) : (
              <Link
                href="/login"
                className="block border border-gray-600 hover:border-gray-500 px-4 py-2 rounded-lg text-gray-300 hover:text-white transition-colors"
                onClick={() => setMenuOpen(false)}
              >
                Login
              </Link>
            )}
          </div>
        )}
      </div>
    </nav>
  );
}
