import Link from 'next/link';
import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/context/AuthContext';

export default function PrivateNavbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <nav className="bg-gray-900 bg-opacity-90 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-6 py-4">
        <div className="flex justify-between items-center">
          <Link href="/private/dashboard" className="text-2xl font-bold">
            PARALLAX<span className="text-blue-500">DEV</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link
              href="/private/dashboard"
              className={`${router.pathname === '/private/dashboard' ? 'text-white' : 'text-gray-300 hover:text-white'} transition-colors`}
            >
              Dashboard
            </Link>
            <Link
              href="/private/blog"
              className={`${router.pathname.startsWith('/private/blog') ? 'text-white' : 'text-gray-300 hover:text-white'} transition-colors`}
            >
              Internal Blog
            </Link>
            <Link
              href="/private/spreadsheets"
              className={`${router.pathname.startsWith('/private/spreadsheets') ? 'text-white' : 'text-gray-300 hover:text-white'} transition-colors`}
            >
              Spreadsheets
            </Link>
            <Link
              href="/private/tools"
              className={`${router.pathname.startsWith('/private/tools') ? 'text-white' : 'text-gray-300 hover:text-white'} transition-colors`}
            >
              Dev Tools
            </Link>

            <Link
              href="/"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Public Site
            </Link>

            <div className="relative ml-4 group">
              <button className="flex items-center space-x-1 text-gray-300 hover:text-white">
                <span>{user?.displayName || user?.email?.split('@')[0] || 'User'}</span>
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg py-2 hidden group-hover:block">
                <div className="px-4 py-2 text-sm text-gray-400 border-b border-gray-700">
                  Logged in as<br />
                  <span className="font-semibold text-white">{user?.email}</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Navigation Button */}
          <button
            className="md:hidden text-gray-300 focus:outline-none"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            {menuOpen ? (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>

        {/* Mobile Navigation Menu */}
        {menuOpen && (
          <div className="md:hidden mt-4 py-4 space-y-4">
            <Link
              href="/private/dashboard"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Dashboard
            </Link>
            <Link
              href="/private/blog"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Internal Blog
            </Link>
            <Link
              href="/private/spreadsheets"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Spreadsheets
            </Link>
            <Link
              href="/private/tools"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Dev Tools
            </Link>
            <Link
              href="/"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Public Site
            </Link>
            <button
              onClick={handleLogout}
              className="block w-full text-left text-red-400 hover:text-red-300 transition-colors"
            >
              Logout
            </button>
          </div>
        )}
      </div>
    </nav>
  );
}
