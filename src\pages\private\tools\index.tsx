import Head from 'next/head';
import { useState, useEffect } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface Tool {
  id: string;
  name: string;
  description: string;
  url: string;
  category: string;
  icon: string;
}

export default function DevToolsPage() {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchTools = async () => {
      try {
        const snapshot = await getDocs(collection(db, 'devtools'));
        const toolsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Tool[];

        setTools(toolsData);
      } catch (error) {
        console.error('Error fetching tools:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTools();
  }, []);

  const filteredTools = tools.filter(tool =>
    tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const categories = Array.from(new Set(tools.map(tool => tool.category)));

  return (
    <ProtectedRoute>
      <Head>
        <title>Developer Tools | Parallax Interactive</title>
        <meta name="description" content="Developer resources and tools for the Parallax Interactive team" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <h1 className="text-4xl font-bold mb-8">Developer Tools</h1>

            <div className="bg-gray-800 rounded-lg p-4 mb-8">
              <input
                type="text"
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-amber-500"></div>
              </div>
            ) : (
              <div className="space-y-8">
                {categories.map(category => {
                  const categoryTools = filteredTools.filter(tool => tool.category === category);
                  if (categoryTools.length === 0) return null;

                  return (
                    <div key={category}>
                      <h2 className="text-2xl font-bold mb-4">{category}</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {categoryTools.map(tool => (
                          <a
                            key={tool.id}
                            href={tool.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-gray-800 rounded-lg p-6 hover:transform hover:scale-105 transition-all border border-gray-700 hover:border-amber-500"
                          >
                            <div className="flex items-start">
                              <div className="bg-amber-900 p-3 rounded-lg mr-4">
                                <svg className="h-6 w-6 text-amber-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tool.icon || "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"} />
                                </svg>
                              </div>
                              <div>
                                <h3 className="text-xl font-bold mb-2">{tool.name}</h3>
                                <p className="text-gray-300">{tool.description}</p>
                              </div>
                            </div>
                          </a>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
