import Head from 'next/head';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageUrl: string;
}

export default function AboutPage() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const snapshot = await getDocs(collection(db, 'team'));
        const members = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as TeamMember[];

        setTeamMembers(members);
      } catch (error) {
        console.error('Error fetching team members:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  return (
    <>
      <Head>
        <title>About | Parallax Interactive</title>
        <meta name="description" content="Learn about Parallax Interactive's history, team, and mission" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        {/* Studio Story */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-5xl font-bold mb-8 text-center">Our Story</h1>
            <div className="prose prose-lg prose-invert mx-auto">
              <p>
                Parallax Interactive was founded in 2018 by a group of passionate developers
                with a shared vision: to create games that challenge conventional design while
                delivering deeply immersive experiences.
              </p>
              <p>
                Starting with a small indie title that gained cult following, we've grown into
                a studio that balances creative experimentation with technical excellence. Our
                team comes from diverse backgrounds across gaming, film, art, and technology.
              </p>
              <p>
                Today, we're focused on developing titles that push the boundaries of interactive
                storytelling while building a community of players and creators who share our passion.
              </p>
            </div>
          </div>
        </section>

        {/* Core Values */}
        <section className="py-20 px-6 bg-gray-900">
          <div className="container mx-auto max-w-4xl">
            <h2 className="text-4xl font-bold mb-12 text-center">Our Core Values</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-gray-800 p-8 rounded-lg">
                <h3 className="text-2xl font-bold mb-4">Creative Innovation</h3>
                <p>We believe in taking creative risks and exploring new gameplay mechanics and narrative approaches that haven't been tried before.</p>
              </div>

              <div className="bg-gray-800 p-8 rounded-lg">
                <h3 className="text-2xl font-bold mb-4">Player-Centric Design</h3>
                <p>Everything we create starts with the player experience in mind, ensuring our games are intuitive yet challenging.</p>
              </div>

              <div className="bg-gray-800 p-8 rounded-lg">
                <h3 className="text-2xl font-bold mb-4">Community Connection</h3>
                <p>We value transparent development and foster genuine relationships with our player community, incorporating their feedback.</p>
              </div>

              <div className="bg-gray-800 p-8 rounded-lg">
                <h3 className="text-2xl font-bold mb-4">Technical Excellence</h3>
                <p>We're committed to high-quality code, optimized performance, and embracing new technologies that enhance our games.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 px-6">
          <div className="container mx-auto">
            <h2 className="text-4xl font-bold mb-12 text-center">Meet Our Team</h2>

            {loading ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {teamMembers.map((member) => (
                  <div key={member.id} className="bg-gray-800 rounded-lg overflow-hidden text-center">
                    <div className="relative h-64 w-64 mx-auto mt-8 rounded-full overflow-hidden">
                      <Image
                        src={member.imageUrl}
                        alt={member.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-2xl font-bold mb-1">{member.name}</h3>
                      <p className="text-blue-400 mb-4">{member.role}</p>
                      <p className="text-gray-300">{member.bio}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
}
