import Head from 'next/head';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { doc, getDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';
import { useAuth } from '@/context/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';
import { MDXEditor } from '@/components/MDXEditor';

export default function EditBlogPostPage() {
  const router = useRouter();
  const { id } = router.query;
  const [title, setTitle] = useState('');
  const [summary, setSummary] = useState('');
  const [content, setContent] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState('');
  const [currentImageUrl, setCurrentImageUrl] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  const { user } = useAuth();

  useEffect(() => {
    const fetchBlogPost = async () => {
      if (!id) return;

      try {
        const docRef = doc(db, 'blog', id as string);
        const docSnap = await getDoc(docRef);

        if (!docSnap.exists()) {
          setError('Blog post not found');
          setLoading(false);
          return;
        }

        const data = docSnap.data();
        setTitle(data.title);
        setSummary(data.summary || '');
        setContent(data.content);
        setIsPublic(data.isPublic);
        setCurrentImageUrl(data.imageUrl || '');
        setImagePreview(data.imageUrl || '');
      } catch (err: any) {
        console.error('Error fetching blog post:', err);
        setError(err.message || 'Failed to load blog post');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPost();
  }, [id]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImageFile(file);

    // Create preview URL
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[^\w ]+/g, '')
      .replace(/ +/g, '-');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    try {
      if (!id) throw new Error('Blog post ID is missing');

      // First upload new image if we have one
      let imageUrl = currentImageUrl;
      if (imageFile) {
        const imageRef = ref(storage, `blog-images/${Date.now()}-${imageFile.name}`);
        await uploadBytes(imageRef, imageFile);
        imageUrl = await getDownloadURL(imageRef);
      }

      // Generate an updated slug from the title
      const slug = generateSlug(title);

      // Update the blog post in Firestore
      const blogRef = doc(db, 'blog', id as string);
      await updateDoc(blogRef, {
        title,
        summary,
        content,
        slug,
        imageUrl,
        isPublic,
        updatedAt: Timestamp.now(),
        updatedBy: user?.uid
      });

      router.push('/private/blog');
    } catch (err: any) {
      console.error('Error updating blog post:', err);
      setError(err.message || 'Failed to update blog post');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Head>
        <title>Edit Blog Post | Parallax Interactive</title>
        <meta name="description" content="Edit an existing blog post for Parallax Interactive" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-4xl font-bold mb-8">Edit Blog Post</h1>

            {error && (
              <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg p-6">
              <div className="mb-6">
                <label htmlFor="title" className="block mb-2 font-semibold">Title</label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="summary" className="block mb-2 font-semibold">Summary</label>
                <textarea
                  id="summary"
                  value={summary}
                  onChange={(e) => setSummary(e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                ></textarea>
              </div>

              <div className="mb-6">
                <label htmlFor="image" className="block mb-2 font-semibold">Featured Image</label>
                <input
                  type="file"
                  id="image"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />

                {imagePreview && (
                  <div className="mt-4">
                    <img
                      src={imagePreview}
                      alt="Image preview"
                      className="h-48 object-cover rounded-lg"
                    />
                  </div>
                )}
              </div>

              <div className="mb-6">
                <label className="block mb-2 font-semibold">Content</label>
                <MDXEditor
                  value={content}
                  onChange={setContent}
                  className="min-h-[400px] bg-gray-700 rounded-lg"
                />
              </div>

              <div className="mb-6 flex items-center">
                <input
                  type="checkbox"
                  id="isPublic"
                  checked={isPublic}
                  onChange={(e) => setIsPublic(e.target.checked)}
                  className="mr-2 h-5 w-5"
                />
                <label htmlFor="isPublic" className="font-semibold">Make this post public</label>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => router.push('/private/blog')}
                  className="px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg transition-all"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-bold transition-all disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
