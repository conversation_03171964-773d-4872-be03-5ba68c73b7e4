import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';
import Markdown from 'react-markdown';

interface BlogPost {
  id: string;
  title: string;
  content: string;
  author: string;
  publishDate: string;
  imageUrl: string;
  isPublic: boolean;
}

export default function PrivateBlogPostViewPage() {
  const router = useRouter();
  const { id } = router.query;
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchBlogPost = async () => {
      if (!id) return;

      try {
        const docRef = doc(db, 'blog', id as string);
        const docSnap = await getDoc(docRef);

        if (!docSnap.exists()) {
          setError('Blog post not found');
          setLoading(false);
          return;
        }

        const data = docSnap.data();
        setPost({
          id: docSnap.id,
          title: data.title,
          content: data.content,
          author: data.author,
          publishDate: data.publishDate.toDate().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          imageUrl: data.imageUrl || '/images/blog-placeholder.jpg',
          isPublic: data.isPublic
        });
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError('Failed to load blog post');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPost();
  }, [id]);

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
          <PrivateNavbar />
          <div className="container mx-auto max-w-4xl py-20 px-6 text-center">
            <h1 className="text-4xl font-bold mb-6">{error}</h1>
            <p className="mb-8">The blog post you're looking for could not be found.</p>
            <Link href="/private/blog" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all">
              Back to Blog
            </Link>
          </div>
          <Footer />
        </div>
      </ProtectedRoute>
    );
  }

  if (!post) return null;

  return (
    <ProtectedRoute>
      <Head>
        <title>{post.title} | Parallax Interactive Blog</title>
        <meta name="description" content={post.content.substring(0, 160)} />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <article className="py-12 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="flex justify-between items-center mb-8">
              <Link href="/private/blog" className="inline-flex items-center text-blue-400 hover:text-blue-300">
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Blog
              </Link>

              <div className="flex space-x-3">
                <Link
                  href={`/private/blog/edit/${post.id}`}
                  className="inline-flex items-center bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Edit Post
                </Link>

                {post.isPublic && (
                  <Link
                    href={`/blog/${post.id}`}
                    className="inline-flex items-center bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-white transition-colors"
                    target="_blank"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    View Public
                  </Link>
                )}
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg overflow-hidden mb-8">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h1 className="text-4xl font-bold">{post.title}</h1>
                  <span className={`px-3 py-1 rounded-full text-sm ${
                    post.isPublic
                      ? 'bg-green-900 text-green-300'
                      : 'bg-gray-700 text-gray-300'
                  }`}>
                    {post.isPublic ? 'Public' : 'Private'}
                  </span>
                </div>

                <div className="flex items-center mb-8 text-gray-400">
                  <span className="mr-4">{post.author}</span>
                  <span>{post.publishDate}</span>
                </div>
              </div>
            </div>

            <div className="relative h-96 w-full mb-10">
              <Image
                src={post.imageUrl}
                alt={post.title}
                fill
                className="object-cover rounded-lg"
              />
            </div>

            <div className="prose prose-lg prose-invert max-w-none">
              <Markdown>{post.content}</Markdown>
            </div>
          </div>
        </article>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
