import Head from 'next/head';
import { useState, useEffect } from 'react';
import { collection, getDocs, addDoc, deleteDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface Snippet {
  id: string;
  title: string;
  code: string;
  description: string;
  language: string;
  createdAt: string;
}

export default function CodeSnippetsPage() {
  const [snippets, setSnippets] = useState<Snippet[]>([]);
  const [loading, setLoading] = useState(true);
  const [newSnippetTitle, setNewSnippetTitle] = useState('');
  const [newSnippetCode, setNewSnippetCode] = useState('');
  const [newSnippetDescription, setNewSnippetDescription] = useState('');
  const [newSnippetLanguage, setNewSnippetLanguage] = useState('javascript');
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchSnippets = async () => {
      try {
        const snapshot = await getDocs(collection(db, 'snippets'));
        const snippetsData = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            code: data.code,
            description: data.description,
            language: data.language,
            createdAt: new Date(data.createdAt.toDate()).toLocaleString()
          };
        });

        setSnippets(snippetsData);
      } catch (err) {
        console.error('Error fetching snippets:', err);
        setError('Failed to load snippets');
      } finally {
        setLoading(false);
      }
    };

    fetchSnippets();
  }, []);

  const addSnippet = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newSnippetTitle || !newSnippetCode) return;

    try {
      await addDoc(collection(db, 'snippets'), {
        title: newSnippetTitle,
        code: newSnippetCode,
        description: newSnippetDescription,
        language: newSnippetLanguage,
        createdAt: Timestamp.now()
      });

      setNewSnippetTitle('');
      setNewSnippetCode('');
      setNewSnippetDescription('');

      // Refresh snippets
      const snapshot = await getDocs(collection(db, 'snippets'));
      const snippetsData = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title,
          code: data.code,
          description: data.description,
          language: data.language,
          createdAt: new Date(data.createdAt.toDate()).toLocaleString()
        };
      });

      setSnippets(snippetsData);
    } catch (err) {
      console.error('Error adding snippet:', err);
      setError('Failed to add snippet');
    }
  };

  const deleteSnippet = async (snippetId: string) => {
    if (!confirm('Are you sure you want to delete this snippet?')) return;

    try {
      await deleteDoc(doc(db, 'snippets', snippetId));
      setSnippets(snippets.filter(snippet => snippet.id !== snippetId));
    } catch (err) {
      console.error('Error deleting snippet:', err);
      setError('Failed to delete snippet');
    }
  };

  return (
    <ProtectedRoute>
      <Head>
        <title>Code Snippets | Parallax Interactive</title>
        <meta name="description" content="Code snippet library for the Parallax Interactive team" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <h1 className="text-4xl font-bold mb-8">Code Snippet Library</h1>

            {error && (
              <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
                {error}
              </div>
            )}

            <div className="bg-gray-800 rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold mb-4">Add New Snippet</h2>

              <form onSubmit={addSnippet} className="space-y-4">
                <div>
                  <label htmlFor="title" className="block mb-2 font-semibold">Title</label>
                  <input
                    type="text"
                    id="title"
                    value={newSnippetTitle}
                    onChange={(e) => setNewSnippetTitle(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block mb-2 font-semibold">Description</label>
                  <textarea
                    id="description"
                    value={newSnippetDescription}
                    onChange={(e) => setNewSnippetDescription(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label htmlFor="language" className="block mb-2 font-semibold">Language</label>
                  <select
                    id="language"
                    value={newSnippetLanguage}
                    onChange={(e) => setNewSnippetLanguage(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="javascript">JavaScript</option>
                    <option value="python">Python</option>
                    <option value="html">HTML</option>
                    <option value="css">CSS</option>
                    <option value="typescript">TypeScript</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="code" className="block mb-2 font-semibold">Code</label>
                  <textarea
                    id="code"
                    value={newSnippetCode}
                    onChange={(e) => setNewSnippetCode(e.target.value)}
                    rows={8}
                    className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all"
                >
                  Add Snippet
                </button>
              </form>
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {snippets.map(snippet => (
                  <div key={snippet.id} className="bg-gray-800 rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-xl font-bold">{snippet.title}</h3>
                      <button
                        onClick={() => deleteSnippet(snippet.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        Delete
                      </button>
                    </div>
                    <p className="text-gray-300 mb-3">{snippet.description}</p>
                    <div className="text-sm text-gray-400 mb-3">Language: {snippet.language}</div>
                    <pre className="bg-gray-700 rounded-lg p-4 overflow-x-auto">
                      <code className="font-mono text-sm">{snippet.code}</code>
                    </pre>
                    <div className="text-sm text-gray-400 mt-4">Created: {snippet.createdAt}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
