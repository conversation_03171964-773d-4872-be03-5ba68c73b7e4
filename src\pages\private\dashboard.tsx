import Head from 'next/head';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface ActivityItem {
  id: string;
  type: 'blog' | 'spreadsheet' | 'project';
  title: string;
  user: string;
  timestamp: string;
}

export default function DashboardPage() {
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        const q = query(
          collection(db, 'activity'),
          orderBy('timestamp', 'desc'),
          limit(10)
        );

        const snapshot = await getDocs(q);
        const activity = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            type: data.type,
            title: data.title,
            user: data.user,
            timestamp: data.timestamp.toDate().toLocaleString()
          };
        });

        setRecentActivity(activity);
      } catch (error) {
        console.error('Error fetching recent activity:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentActivity();
  }, []);

  return (
    <ProtectedRoute>
      <Head>
        <title>Developer Dashboard | Parallax Interactive</title>
        <meta name="description" content="Private developer dashboard for Parallax Interactive team members" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <h1 className="text-4xl font-bold mb-8">Developer Dashboard</h1>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <Link href="/private/blog" className="bg-indigo-900 hover:bg-indigo-800 p-6 rounded-lg transition-all">
                <div className="flex items-center mb-4">
                  <div className="bg-indigo-700 p-3 rounded-lg mr-4">
                    <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold">Internal Blog</h2>
                </div>
                <p className="text-indigo-200">Write and manage internal development updates and documentation</p>
              </Link>

              <Link href="/private/spreadsheets" className="bg-emerald-900 hover:bg-emerald-800 p-6 rounded-lg transition-all">
                <div className="flex items-center mb-4">
                  <div className="bg-emerald-700 p-3 rounded-lg mr-4">
                    <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold">Spreadsheets</h2>
                </div>
                <p className="text-emerald-200">Access and collaborate on project spreadsheets, budgets, and timelines</p>
              </Link>

              <Link href="/private/tools" className="bg-amber-900 hover:bg-amber-800 p-6 rounded-lg transition-all">
                <div className="flex items-center mb-4">
                  <div className="bg-amber-700 p-3 rounded-lg mr-4">
                    <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold">Dev Tools</h2>
                </div>
                <p className="text-amber-200">Access development tools, code snippets, and project management resources</p>
              </Link>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6">Recent Activity</h2>

              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-4 border-blue-500"></div>
                </div>
              ) : (
                <div className="divide-y divide-gray-700">
                  {recentActivity.map((item) => (
                    <div key={item.id} className="py-4 flex items-start">
                      <div className={`p-2 rounded-lg mr-4 ${
                        item.type === 'blog'
                          ? 'bg-indigo-900 text-indigo-300'
                          : item.type === 'spreadsheet'
                            ? 'bg-emerald-900 text-emerald-300'
                            : 'bg-amber-900 text-amber-300'
                      }`}>
                        {item.type === 'blog' && (
                          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        )}
                        {item.type === 'spreadsheet' && (
                          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        )}
                        {item.type === 'project' && (
                          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        )}
                      </div>
                      <div className="flex-grow">
                        <h3 className="font-semibold">{item.title}</h3>
                        <div className="flex justify-between text-sm text-gray-400 mt-1">
                          <span>{item.user}</span>
                          <span>{item.timestamp}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}