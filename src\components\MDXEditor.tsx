import { useRef, useEffect } from 'react';
import 'codemirror/lib/codemirror.css';
import '@toast-ui/editor/dist/toastui-editor.css';

interface MDXEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function MDXEditor({ value, onChange, className = '' }: MDXEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const editorInstanceRef = useRef<any>(null);

  useEffect(() => {
    const loadEditor = async () => {
      if (editorRef.current) {
        // Dynamically import the editor to avoid SSR issues
        const { Editor } = await import('@toast-ui/editor');

        const editorInstance = new Editor({
          el: editorRef.current,
          initialValue: value,
          previewStyle: 'tab',
          height: '500px',
          initialEditType: 'markdown',
          toolbarItems: [
            ['heading', 'bold', 'italic', 'strike'],
            ['hr', 'quote'],
            ['ul', 'ol', 'task', 'indent', 'outdent'],
            ['table', 'image', 'link'],
            ['code', 'codeblock'],
            ['scrollSync']
          ],
          events: {
            change: () => {
              onChange(editorInstance.getMarkdown());
            }
          }
        });

        editorInstanceRef.current = editorInstance;
      }
    };

    loadEditor();

    return () => {
      if (editorInstanceRef.current) {
        editorInstanceRef.current.destroy();
      }
    };
  }, []);

  // Update the editor content if value prop changes from outside
  useEffect(() => {
    if (editorInstanceRef.current && editorInstanceRef.current.getMarkdown() !== value) {
      editorInstanceRef.current.setMarkdown(value);
    }
  }, [value]);

  return <div ref={editorRef} className={className} />;
}
