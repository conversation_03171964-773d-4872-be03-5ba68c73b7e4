import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface Spreadsheet {
  id: string;
  title: string;
  description: string;
  embedUrl: string;
  category: string;
  createdBy: string;
  createdAt: string;
}

export default function SpreadsheetViewPage() {
  const router = useRouter();
  const { id } = router.query;
  const [spreadsheet, setSpreadsheet] = useState<Spreadsheet | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchSpreadsheet = async () => {
      if (!id) return;

      try {
        const docRef = doc(db, 'spreadsheets', id as string);
        const docSnap = await getDoc(docRef);

        if (!docSnap.exists()) {
          setError('Spreadsheet not found');
          setLoading(false);
          return;
        }

        const data = docSnap.data();
        setSpreadsheet({
          id: docSnap.id,
          title: data.title,
          description: data.description,
          embedUrl: data.embedUrl,
          category: data.category,
          createdBy: data.createdBy,
          createdAt: data.createdAt.toDate().toLocaleDateString()
        });
      } catch (err) {
        console.error('Error fetching spreadsheet:', err);
        setError('Failed to load spreadsheet');
      } finally {
        setLoading(false);
      }
    };

    fetchSpreadsheet();
  }, [id]);

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-emerald-500"></div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
          <PrivateNavbar />
          <div className="container mx-auto max-w-4xl py-20 px-6 text-center">
            <h1 className="text-4xl font-bold mb-6">{error}</h1>
            <p className="mb-8">The spreadsheet you're looking for could not be found.</p>
            <Link href="/private/spreadsheets" className="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-6 rounded-lg transition-all">
              Back to Spreadsheets
            </Link>
          </div>
          <Footer />
        </div>
      </ProtectedRoute>
    );
  }

  if (!spreadsheet) return null;

  return (
    <ProtectedRoute>
      <Head>
        <title>{spreadsheet.title} | Parallax Interactive</title>
        <meta name="description" content={spreadsheet.description} />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <Link href="/private/spreadsheets" className="inline-flex items-center text-emerald-400 hover:text-emerald-300 mb-6">
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Spreadsheets
            </Link>

            <div className="bg-gray-800 rounded-lg overflow-hidden mb-8">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h1 className="text-3xl font-bold">{spreadsheet.title}</h1>
                  <span className="bg-emerald-900 text-emerald-300 px-3 py-1 rounded-full text-sm">
                    {spreadsheet.category}
                  </span>
                </div>
                <p className="text-gray-300 mb-4">{spreadsheet.description}</p>
                <div className="text-sm text-gray-400 mb-4">
                  <p>Created by: {spreadsheet.createdBy}</p>
                  <p>Date: {spreadsheet.createdAt}</p>
                </div>
                <a
                  href={spreadsheet.embedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-emerald-400 hover:text-emerald-300"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Open in New Tab
                </a>
              </div>
            </div>

            {/* Embedded Spreadsheet */}
            <div className="bg-white rounded-lg overflow-hidden aspect-video w-full">
              <iframe
                src={spreadsheet.embedUrl}
                className="w-full h-full"
                frameBorder="0"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
