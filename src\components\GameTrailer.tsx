interface GameTrailerProps {
  videoId: string;
}

export default function GameTrailer({ videoId }: GameTrailerProps) {
  return (
    <div className="aspect-video w-full rounded-lg overflow-hidden">
      <iframe
        src={`https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=0`}
        className="w-full h-full"
        title="Game Trailer"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      ></iframe>
    </div>
  );
}
