import Head from 'next/head';
import { useState, useEffect } from 'react';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'inProgress' | 'done';
  createdAt: string;
}

export default function TaskBoardPage() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        const snapshot = await getDocs(collection(db, 'tasks'));
        const tasksData = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            description: data.description,
            status: data.status,
            createdAt: new Date(data.createdAt.toDate()).toLocaleString()
          };
        });

        setTasks(tasksData);
      } catch (err) {
        console.error('Error fetching tasks:', err);
        setError('Failed to load tasks');
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, []);

  const addTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTaskTitle) return;

    try {
      await addDoc(collection(db, 'tasks'), {
        title: newTaskTitle,
        description: newTaskDescription,
        status: 'todo',
        createdAt: Timestamp.now()
      });

      setNewTaskTitle('');
      setNewTaskDescription('');

      // Refresh tasks
      const snapshot = await getDocs(collection(db, 'tasks'));
      const tasksData = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title,
          description: data.description,
          status: data.status,
          createdAt: new Date(data.createdAt.toDate()).toLocaleString()
        };
      });

      setTasks(tasksData);
    } catch (err) {
      console.error('Error adding task:', err);
      setError('Failed to add task');
    }
  };

  const updateTaskStatus = async (taskId: string, newStatus: 'todo' | 'inProgress' | 'done') => {
    try {
      await updateDoc(doc(db, 'tasks', taskId), {
        status: newStatus
      });

      setTasks(tasks.map(task =>
        task.id === taskId
          ? { ...task, status: newStatus }
          : task
      ));
    } catch (err) {
      console.error('Error updating task status:', err);
      setError('Failed to update task status');
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
      await deleteDoc(doc(db, 'tasks', taskId));
      setTasks(tasks.filter(task => task.id !== taskId));
    } catch (err) {
      console.error('Error deleting task:', err);
      setError('Failed to delete task');
    }
  };

  return (
    <ProtectedRoute>
      <Head>
        <title>Task Board | Parallax Interactive</title>
        <meta name="description" content="Task board for managing team tasks" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-6xl">
            <h1 className="text-4xl font-bold mb-8">Task Board</h1>

            {error && (
              <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
                {error}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* To Do */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">To Do</h2>

                <form onSubmit={addTask} className="mb-6">
                  <input
                    type="text"
                    placeholder="Task title"
                    value={newTaskTitle}
                    onChange={(e) => setNewTaskTitle(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mb-3"
                    required
                  />
                  <textarea
                    placeholder="Task description"
                    value={newTaskDescription}
                    onChange={(e) => setNewTaskDescription(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mb-3"
                  />
                  <button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all"
                  >
                    Add Task
                  </button>
                </form>

                <div className="space-y-4">
                  {tasks
                    .filter(task => task.status === 'todo')
                    .map(task => (
                      <div key={task.id} className="bg-gray-700 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="text-xl font-semibold">{task.title}</h3>
                          <button
                            onClick={() => deleteTask(task.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                        <p className="text-gray-300 mb-3">{task.description}</p>
                        <div className="text-sm text-gray-400">Created: {task.createdAt}</div>
                        <div className="flex justify-end space-x-2 mt-4">
                          <button
                            onClick={() => updateTaskStatus(task.id, 'inProgress')}
                            className="px-3 py-1 rounded bg-yellow-600 hover:bg-yellow-700 text-white"
                          >
                            Start
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* In Progress */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">In Progress</h2>

                <div className="space-y-4">
                  {tasks
                    .filter(task => task.status === 'inProgress')
                    .map(task => (
                      <div key={task.id} className="bg-gray-700 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="text-xl font-semibold">{task.title}</h3>
                          <button
                            onClick={() => deleteTask(task.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                        <p className="text-gray-300 mb-3">{task.description}</p>
                        <div className="text-sm text-gray-400">Created: {task.createdAt}</div>
                        <div className="flex justify-end space-x-2 mt-4">
                          <button
                            onClick={() => updateTaskStatus(task.id, 'todo')}
                            className="px-3 py-1 rounded bg-blue-600 hover:bg-blue-700 text-white"
                          >
                            Back to To Do
                          </button>
                          <button
                            onClick={() => updateTaskStatus(task.id, 'done')}
                            className="px-3 py-1 rounded bg-green-600 hover:bg-green-700 text-white"
                          >
                            Complete
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Done */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">Done</h2>

                <div className="space-y-4">
                  {tasks
                    .filter(task => task.status === 'done')
                    .map(task => (
                      <div key={task.id} className="bg-gray-700 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="text-xl font-semibold">{task.title}</h3>
                          <button
                            onClick={() => deleteTask(task.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                        <p className="text-gray-300 mb-3">{task.description}</p>
                        <div className="text-sm text-gray-400">Created: {task.createdAt}</div>
                        <div className="flex justify-end space-x-2 mt-4">
                          <button
                            onClick={() => updateTaskStatus(task.id, 'inProgress')}
                            className="px-3 py-1 rounded bg-yellow-600 hover:bg-yellow-700 text-white"
                          >
                            Reopen
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
