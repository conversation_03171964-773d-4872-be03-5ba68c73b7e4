import Head from 'next/head';
import { useState } from 'react';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

export default function DonatePage() {
  const [amount, setAmount] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      // Here you would typically integrate with a payment processor like Stripe
      // For now, we'll just record the donation intent in Firestore
      await addDoc(collection(db, 'donations'), {
        amount: parseFloat(amount),
        name,
        email,
        message,
        status: 'pending',
        createdAt: new Date()
      });

      setSuccess(true);
      setAmount('');
      setName('');
      setEmail('');
      setMessage('');
    } catch (err) {
      console.error('Error processing donation:', err);
      setError('An error occurred processing your donation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Support Us | Parallax Interactive</title>
        <meta name="description" content="Support Parallax Interactive's game development efforts through donations and funding" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        {/* Hero Section */}
        <section className="py-20 px-6 text-center">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-5xl font-bold mb-6">Support Our Vision</h1>
            <p className="text-xl mb-8">
              Your contribution helps us continue creating innovative, independent games
              without compromising our creative vision.
            </p>
          </div>
        </section>

        {/* Funding Tiers */}
        <section className="py-12 px-6 bg-gray-900">
          <div className="container mx-auto max-w-6xl">
            <h2 className="text-3xl font-bold mb-12 text-center">Supporter Tiers</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-gray-800 rounded-lg p-8 border border-gray-700 hover:border-blue-500 transition-all">
                <h3 className="text-2xl font-bold mb-4">Community Supporter</h3>
                <p className="text-3xl font-bold mb-6">$5 / month</p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Access to supporter-only Discord channels
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Name in game credits
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Monthly development updates
                  </li>
                </ul>
                <button className="w-full bg-blue-600 hover:bg-blue-700 py-3 rounded-lg font-bold transition-all">
                  Become a Supporter
                </button>
              </div>

              <div className="bg-gray-800 rounded-lg p-8 border-2 border-blue-500 transform scale-105">
                <div className="bg-blue-500 text-white text-sm font-bold py-1 px-3 rounded-full inline-block mb-4">MOST POPULAR</div>
                <h3 className="text-2xl font-bold mb-4">Development Partner</h3>
                <p className="text-3xl font-bold mb-6">$15 / month</p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    All Community Supporter benefits
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Early access to game builds
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Vote on development priorities
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Digital art pack and soundtrack
                  </li>
                </ul>
                <button className="w-full bg-blue-600 hover:bg-blue-700 py-3 rounded-lg font-bold transition-all">
                  Become a Partner
                </button>
              </div>

              <div className="bg-gray-800 rounded-lg p-8 border border-gray-700 hover:border-blue-500 transition-all">
                <h3 className="text-2xl font-bold mb-4">Studio Investor</h3>
                <p className="text-3xl font-bold mb-6">$50 / month</p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    All Development Partner benefits
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Quarterly video calls with the team
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Name a character or location in-game
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Physical collector's edition of our games
                  </li>
                </ul>
                <button className="w-full bg-blue-600 hover:bg-blue-700 py-3 rounded-lg font-bold transition-all">
                  Become an Investor
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* One-time Donation Form */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-2xl">
            <h2 className="text-3xl font-bold mb-8 text-center">Make a One-time Donation</h2>

            {success ? (
              <div className="bg-green-500 bg-opacity-20 border border-green-500 text-green-300 p-4 rounded-lg mb-8">
                Thank you for your generous support! Your contribution helps us continue our work.
              </div>
            ) : null}

            {error ? (
              <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-8">
                {error}
              </div>
            ) : null}

            <form onSubmit={handleSubmit} className="bg-gray-800 p-8 rounded-lg">
              <div className="mb-6">
                <label htmlFor="amount" className="block mb-2 font-semibold">Donation Amount ($)</label>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  min="1"
                  step="1"
                  required
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-6">
                <label htmlFor="name" className="block mb-2 font-semibold">Your Name</label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-6">
                <label htmlFor="email" className="block mb-2 font-semibold">Email Address</label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-6">
                <label htmlFor="message" className="block mb-2 font-semibold">Message (Optional)</label>
                <textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all disabled:opacity-50"
              >
                {loading ? 'Processing...' : 'Submit Donation'}
              </button>
            </form>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
}
