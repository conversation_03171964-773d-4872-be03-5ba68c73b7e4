import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface BlogPost {
  id: string;
  title: string;
  summary: string;
  content: string;
  author: string;
  publishDate: string;
  imageUrl: string;
  slug: string;
  isPublic: boolean;
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        const q = query(
          collection(db, 'blog'),
          where('isPublic', '==', true),
          orderBy('publishDate', 'desc')
        );

        const snapshot = await getDocs(q);
        const blogPosts = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            summary: data.summary,
            content: data.content,
            author: data.author,
            publishDate: data.publishDate.toDate().toISOString().split('T')[0],
            imageUrl: data.imageUrl || '/images/blog-placeholder.jpg',
            slug: data.slug,
            isPublic: data.isPublic
          };
        });

        setPosts(blogPosts);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, []);

  return (
    <>
      <Head>
        <title>Dev Blog | Parallax Interactive</title>
        <meta name="description" content="Development updates, behind-the-scenes insights, and announcements from Parallax Interactive" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        <section className="py-20 px-6">
          <div className="container mx-auto max-w-6xl">
            <h1 className="text-5xl font-bold mb-8 text-center">Dev Blog</h1>
            <p className="text-xl mb-12 text-center max-w-3xl mx-auto">
              Insights, updates, and behind-the-scenes looks at our game development process
            </p>

            {loading ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {posts.map((post) => (
                  <Link key={post.id} href={`/blog/${post.slug}`} className="block">
                    <div className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all h-full flex flex-col">
                      <div className="relative h-48">
                        <Image
                          src={post.imageUrl}
                          alt={post.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="p-6 flex-grow flex flex-col">
                        <div className="flex justify-between items-center mb-3 text-sm text-gray-400">
                          <span>{post.author}</span>
                          <span>{post.publishDate}</span>
                        </div>
                        <h3 className="text-2xl font-bold mb-2">{post.title}</h3>
                        <p className="text-gray-300 mb-4">{post.summary}</p>
                        <span className="text-blue-400 hover:text-blue-300 mt-auto">Read more →</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
}
