import Head from 'next/head';
import ProtectedRoute from '@/components/ProtectedRoute';
import PrivateNavbar from '@/components/PrivateNavbar';
import Footer from '@/components/Footer';

export default function VersionControlPage() {
  return (
    <ProtectedRoute>
      <Head>
        <title>Version Control | Parallax Interactive</title>
        <meta name="description" content="Version control resources for the Parallax Interactive team" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <PrivateNavbar />

        <section className="py-12 px-6">
          <div className="container mx-auto max-w-4xl">
            <h1 className="text-4xl font-bold mb-8">Version Control</h1>

            <p className="text-xl mb-8">
              Effective version control is crucial for collaborative game development. Here are some resources to help you manage your projects with Git:
            </p>

            <div className="space-y-6">
              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">Git Documentation</h2>
                <p className="text-gray-300 mb-4">
                  Official documentation for Git, covering everything from basic commands to advanced workflows.
                </p>
                <a
                  href="https://git-scm.com/doc"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all inline-block"
                >
                  View Documentation
                </a>
              </div>

              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">Git Best Practices</h2>
                <p className="text-gray-300 mb-4">
                  A guide to best practices for using Git in a team environment, including branching strategies and commit message conventions.
                </p>
                <a
                  href="https://www.atlassian.com/git/tutorials/comparing-workflows"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all inline-block"
                >
                  Learn More
                </a>
              </div>

              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">GitHub Tutorials</h2>
                <p className="text-gray-300 mb-4">
                  Tutorials and guides for using GitHub, a popular platform for hosting Git repositories.
                </p>
                <a
                  href="https://guides.github.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all inline-block"
                >
                  Explore Tutorials
                </a>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </ProtectedRoute>
  );
}
