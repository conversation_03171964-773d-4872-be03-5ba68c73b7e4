import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import GameTrailer from '@/components/GameTrailer';

export default function HomePage() {
  const [featuredProjects, setFeaturedProjects] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedProjects = async () => {
      try {
        const q = query(
          collection(db, 'projects'),
          where('featured', '==', true),
          orderBy('releaseDate', 'desc'),
          limit(3)
        );

        const snapshot = await getDocs(q);
        const projects = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        setFeaturedProjects(projects);
      } catch (error) {
        console.error('Error fetching featured projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProjects();
  }, []);

  return (
    <>
      <Head>
        <title>Parallax Interactive | Game Development Studio</title>
        <meta name="description" content="Innovative game development studio creating unique gaming experiences" />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <Navbar />

        {/* Hero Section */}
        <section className="relative h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0 opacity-50">
            {/* Background animation or image here */}
          </div>

          <div className="container mx-auto px-6 z-10 text-center">
            <h1 className="text-5xl md:text-7xl font-extrabold mb-6 animate-fade-in-up">
              PARALLAX INTERACTIVE
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Creating immersive gaming experiences that push the boundaries of interactive storytelling
            </p>
            <div className="flex justify-center gap-4 mt-8">
              <Link href="/about" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all">
                About Us
              </Link>
              <Link href="/roadmap" className="bg-transparent hover:bg-white/10 border border-white text-white font-bold py-3 px-6 rounded-lg transition-all">
                View Roadmap
              </Link>
            </div>
          </div>
        </section>

        {/* Featured Projects */}
        <section className="py-20 px-6">
          <div className="container mx-auto">
            <h2 className="text-4xl font-bold mb-12 text-center">Featured Projects</h2>

            {loading ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {featuredProjects.map((project) => (
                  <div key={project.id} className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all">
                    <div className="relative h-48">
                      <Image
                        src={project.thumbnail}
                        alt={project.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-2xl font-bold mb-2">{project.title}</h3>
                      <p className="text-gray-300 mb-4">{project.description}</p>
                      <Link href={`/projects/${project.id}`} className="text-blue-400 hover:text-blue-300">
                        Learn more →
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Latest Trailer */}
        <section className="py-20 px-6 bg-gray-900">
          <div className="container mx-auto">
            <h2 className="text-4xl font-bold mb-12 text-center">Latest Trailer</h2>
            <div className="max-w-4xl mx-auto">
              <GameTrailer videoId="latest-game-trailer-id" />
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
}